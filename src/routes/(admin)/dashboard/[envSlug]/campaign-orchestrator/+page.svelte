<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import {
    Megaphone,
    Download,
    Calendar,
    Users,
    Clock,
    User,
    Bot,
    ChevronRight,
    Copy,
    Zap,
    Mail,
    Share2,
    CheckCircle2,
    Loader2,
    Circle,
  } from "lucide-svelte"
  import { onMount } from "svelte"
  import { slide, fade } from "svelte/transition"

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: "pending" | "active" | "completed"
    progress?: number
  }

  const messages = writable<Message[]>([])
  let input = ""
  let isLoading = false
  let placeholderIndex = 0
  let currentPlaceholder = ""
  let progressSteps: ProgressStep[] = []
  let currentProgress = 0

  // Animated placeholder examples
  const placeholderExamples = [
    "Create a multi-channel campaign for our new product launch...",
    "Plan a seasonal marketing campaign for Black Friday...",
    "Design a customer retention campaign with email sequences...",
    "Orchestrate a brand awareness campaign across social media...",
  ]

  // Interactive card templates
  const interactiveCards = [
    {
      icon: Zap,
      title: "Product Launch Campaign",
      description:
        "Multi-phase campaign strategy for new product introductions",
      prompt:
        "Create a comprehensive product launch campaign for [Product Name] targeting [Audience] with a [Timeline] timeline",
    },
    {
      icon: Calendar,
      title: "Seasonal Promotion",
      description:
        "Coordinated campaigns for holidays, events, and seasonal sales",
      prompt:
        "Plan a [Holiday/Season] marketing campaign across email, social, and paid channels with a budget of [Budget]",
    },
    {
      icon: Users,
      title: "Customer Retention",
      description:
        "Multi-touch engagement campaigns to reduce churn and increase LTV",
      prompt:
        "Design a customer retention campaign with personalized email sequences and loyalty rewards for [Customer Segment]",
    },
  ]

  function generateId(): string {
    return Math.random().toString(36).substring(2, 11)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    const userMessage = input.trim()
    input = ""
    isLoading = true

    // Initialize progress steps
    progressSteps = [
      {
        id: 1,
        title: "Campaign Analysis",
        description: "Understanding your objectives...",
        status: "pending",
      },
      {
        id: 2,
        title: "Audience Segmentation",
        description: "Identifying target segments...",
        status: "pending",
      },
      {
        id: 3,
        title: "Channel Strategy",
        description: "Planning channel distribution...",
        status: "pending",
      },
      {
        id: 4,
        title: "Content Planning",
        description: "Creating content strategy...",
        status: "pending",
      },
      {
        id: 5,
        title: "Timeline & Budget",
        description: "Optimizing resources...",
        status: "pending",
      },
      {
        id: 6,
        title: "Campaign Blueprint",
        description: "Finalizing your plan...",
        status: "pending",
      },
    ]
    currentProgress = 0

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      },
    ])

    // Simulate response (placeholder for actual implementation)
    setTimeout(() => {
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content: `# Campaign Orchestration Plan

I'll help you create a comprehensive campaign strategy. This feature is coming soon and will include:

## 🎯 Campaign Overview
- **Objective**: Define clear campaign goals and KPIs
- **Target Audience**: Detailed persona mapping
- **Timeline**: Phase-by-phase execution plan
- **Budget Allocation**: Channel-wise budget distribution

## 📱 Channel Strategy
### Email Marketing
- Automated sequences and personalization
- A/B testing recommendations
- Optimal send times

### Social Media
- Platform-specific content calendars
- Engagement strategies
- Influencer partnerships

### Content Marketing
- Blog post schedule
- SEO optimization
- Lead magnets

### Paid Advertising
- Channel selection (Google, Meta, LinkedIn)
- Budget optimization
- Creative recommendations

## 📊 Performance Metrics
- Real-time dashboard
- Conversion tracking
- ROI measurement

Stay tuned for the full Catalyst experience!`,
          timestamp: new Date(),
          isReport: true,
        },
      ])
      isLoading = false
    }, 2000)
  }

  function handleKeyDown(e: KeyboardEvent) {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  function formatContent(content: string): string {
    // Basic markdown to HTML conversion
    return content
      .replace(
        /^### (.+)$/gm,
        '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>',
      )
      .replace(/^## (.+)$/gm, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
      .replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>')
      .replace(/^\* (.+)$/gm, '<li class="ml-4">• $1</li>')
      .replace(/^- (.+)$/gm, '<li class="ml-4">• $1</li>')
      .replace(/\*\*(.+?)\*\*/g, "<strong>$1</strong>")
      .replace(/\n\n/g, '</p><p class="mb-4">')
      .replace(/^/, '<p class="mb-4">')
      .replace(/$/, "</p>")
  }

  function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text)
  }

  function downloadAsMarkdown(message: Message) {
    const blob = new Blob([message.content], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `campaign-plan-${new Date().toISOString().split("T")[0]}.md`
    a.click()
  }

  // Update placeholder animation
  onMount(() => {
    currentPlaceholder = placeholderExamples[0]
    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 3000)
    return () => clearInterval(interval)
  })

  // Simulate progress updates
  let progressInterval: NodeJS.Timeout | undefined

  $: if (isLoading && progressSteps.length > 0) {
    progressInterval = setInterval(() => {
      const pendingStep = progressSteps.find(
        (step) => step.status === "pending",
      )
      const activeStep = progressSteps.find((step) => step.status === "active")

      if (activeStep && activeStep.progress !== undefined) {
        activeStep.progress = Math.min(100, activeStep.progress + 20)
        if (activeStep.progress === 100) {
          activeStep.status = "completed"
          currentProgress = Math.round(
            (progressSteps.filter((s) => s.status === "completed").length /
              progressSteps.length) *
              100,
          )
        }
      } else if (pendingStep) {
        pendingStep.status = "active"
        pendingStep.progress = 0
      } else {
        clearInterval(progressInterval)
      }
      progressSteps = [...progressSteps]
    }, 300)
  } else if (progressInterval) {
    clearInterval(progressInterval)
  }
</script>

<svelte:head>
  <title>Catalyst - AI Campaign Orchestrator</title>
</svelte:head>

<div class="h-screen flex flex-col" style="background: var(--background);">
  <!-- Header -->
  <div
    class="border-b-2 flex-shrink-0"
    style="border-color: var(--border); background: var(--background);"
  >
    <div class="max-w-7xl mx-auto px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div
            class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer"
            style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <Megaphone
              class="w-6 h-6 animate-pulse"
              style="color: var(--primary-foreground);"
            />
          </div>
          <div>
            <h1 class="text-3xl font-black" style="color: var(--foreground);">
              Catalyst
            </h1>
            <p
              class="text-lg font-medium"
              style="color: var(--muted-foreground);"
            >
              AI-powered multi-channel campaign planning and execution
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div
            class="flex items-center space-x-2 px-4 py-2 border-2"
            style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <Share2 class="w-4 h-4" style="color: var(--accent-foreground);" />
            <span
              class="text-sm font-bold"
              style="color: var(--accent-foreground);">Multi-Channel</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Breadcrumb Navigation -->
  <div class="max-w-7xl px-6 lg:px-8 py-4">
    <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
      <a
        href="/dashboard/{$page.params.envSlug}"
        class="hover:text-foreground transition-colors"
      >
        Dashboard
      </a>
      <ChevronRight class="w-4 h-4" />
      <span class="text-foreground font-medium">Catalyst</span>
    </nav>
  </div>

  <div
    class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"
  >
    <!-- Full Width Conversation Panel -->
    <div class="h-full">
      <div
        class="card-brutal p-0 chat-container h-full"
        style="background: var(--card);"
      >
        <!-- Messages Area -->
        <div class="messages-wrapper messages-wrapper-seo messages-container">
          <div class="space-y-6">
            {#if $messages.length === 0}
              <div class="text-center py-12">
                <div
                  class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2"
                  style="background: var(--muted); border-color: var(--border);"
                >
                  <Megaphone
                    class="w-8 h-8"
                    style="color: var(--muted-foreground);"
                  />
                </div>
                <h3
                  class="text-xl font-bold mb-2"
                  style="color: var(--foreground);"
                >
                  Start Planning Your Campaign
                </h3>
                <p
                  class="font-medium mb-6"
                  style="color: var(--muted-foreground);"
                >
                  Create comprehensive multi-channel marketing campaigns with AI
                </p>

                <!-- Interactive Cards -->
                <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
                  {#each interactiveCards as card}
                    <button
                      on:click={() => {
                        input = card.prompt
                      }}
                      class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group"
                      style="background: var(--card); border-color: var(--border);"
                      disabled={isLoading}
                    >
                      <div class="flex items-center gap-3 mb-3">
                        <div
                          class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform"
                          style="background: var(--primary); border-color: var(--border);"
                        >
                          <svelte:component
                            this={card.icon}
                            class="w-4 h-4"
                            style="color: var(--primary-foreground);"
                          />
                        </div>
                        <h4
                          class="font-bold text-sm"
                          style="color: var(--foreground);"
                        >
                          {card.title}
                        </h4>
                      </div>
                      <p
                        class="text-xs mb-3"
                        style="color: var(--muted-foreground);"
                      >
                        {card.description}
                      </p>
                      <div
                        class="text-xs font-mono p-2 border-2 rounded"
                        style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);"
                      >
                        {card.prompt}
                      </div>
                    </button>
                  {/each}
                </div>
              </div>
            {/if}

            {#each $messages as message}
              <div
                class="flex gap-4 {message.role === 'user'
                  ? 'flex-row-reverse'
                  : ''}"
              >
                <div
                  class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                  style="background: var(--{message.role === 'user'
                    ? 'primary'
                    : 'secondary'}); border-color: var(--border); box-shadow: var(--shadow-sm);"
                >
                  {#if message.role === "user"}
                    <User
                      class="w-5 h-5"
                      style="color: var(--primary-foreground);"
                    />
                  {:else}
                    <Bot
                      class="w-5 h-5"
                      style="color: var(--secondary-foreground);"
                    />
                  {/if}
                </div>

                <div class="flex-1 max-w-3xl">
                  <div class="flex items-center gap-2 mb-2">
                    <span
                      class="text-sm font-bold"
                      style="color: var(--foreground);"
                    >
                      {message.role === "user" ? "You" : "Catalyst"}
                    </span>
                    <div class="flex items-center gap-1">
                      <Clock
                        class="w-3 h-3"
                        style="color: var(--muted-foreground);"
                      />
                      <span
                        class="text-xs"
                        style="color: var(--muted-foreground);"
                      >
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    {#if message.role === "assistant" && message.isReport}
                      <button
                        on:click={() => downloadAsMarkdown(message)}
                        class="btn-secondary px-2 py-1 text-xs flex items-center gap-1"
                        title="Download as Markdown"
                      >
                        <Download class="w-3 h-3" />
                        Download
                      </button>
                    {/if}
                  </div>

                  {#if message.role === "user"}
                    <div
                      class="p-4 border-2"
                      style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                    >
                      <p
                        class="font-medium"
                        style="color: var(--primary-foreground);"
                      >
                        {message.content}
                      </p>
                    </div>
                  {:else}
                    <div
                      class="p-6 border-2 mb-4"
                      style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"
                    >
                      <div class="prose prose-sm max-w-none">
                        {@html formatContent(message.content)}
                      </div>
                    </div>

                    <!-- Follow-up Actions -->
                    <div class="flex flex-wrap gap-2 mb-4">
                      <button
                        on:click={() => copyToClipboard(message.content)}
                        class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                      >
                        <Copy class="w-3 h-3" />
                        Copy
                      </button>
                      <button
                        class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                      >
                        <Mail class="w-3 h-3" />
                        Email Brief
                      </button>
                      <button
                        class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                      >
                        <Calendar class="w-3 h-3" />
                        Export Timeline
                      </button>
                    </div>
                  {/if}
                </div>
              </div>
            {/each}

            {#if isLoading}
              <div class="flex gap-4">
                <div
                  class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                  style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                >
                  <Bot
                    class="w-5 h-5"
                    style="color: var(--secondary-foreground);"
                  />
                </div>
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span
                      class="text-sm font-bold"
                      style="color: var(--foreground);">Catalyst</span
                    >
                    <span
                      class="text-xs"
                      style="color: var(--muted-foreground);"
                      >Planning your campaign...</span
                    >
                  </div>
                  <div
                    class="p-6 border-2"
                    style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"
                  >
                    <!-- Progress Timeline -->
                    <div class="space-y-4">
                      {#each progressSteps as step (step.id)}
                        <div
                          class="flex items-start gap-3"
                          transition:slide={{ duration: 300 }}
                        >
                          <div class="flex-shrink-0 mt-0.5">
                            {#if step.status === "completed"}
                              <div transition:fade={{ duration: 200 }}>
                                <CheckCircle2
                                  class="w-5 h-5 animate-scale-in"
                                  style="color: var(--primary);"
                                />
                              </div>
                            {:else if step.status === "active"}
                              <Loader2
                                class="w-5 h-5 animate-spin"
                                style="color: var(--primary);"
                              />
                            {:else}
                              <Circle
                                class="w-5 h-5 opacity-30"
                                style="color: var(--muted-foreground);"
                              />
                            {/if}
                          </div>
                          <div class="flex-1">
                            <h4
                              class="text-sm font-bold mb-1"
                              style="color: {step.status === 'pending'
                                ? 'var(--muted-foreground)'
                                : 'var(--foreground)'};{step.status ===
                              'pending'
                                ? 'opacity: 0.5'
                                : ''}"
                            >
                              {step.title}
                            </h4>
                            <p
                              class="text-xs"
                              style="color: var(--muted-foreground);{step.status ===
                              'pending'
                                ? 'opacity: 0.5'
                                : ''}"
                            >
                              {step.description}
                            </p>
                            {#if step.status === "active" && step.progress}
                              <div
                                class="mt-2 h-1 rounded-full overflow-hidden"
                                style="background: var(--muted);"
                              >
                                <div
                                  class="h-full transition-all duration-500 ease-out"
                                  style="background: var(--primary); width: {step.progress}%"
                                ></div>
                              </div>
                            {/if}
                          </div>
                        </div>
                      {/each}
                    </div>

                    <!-- Overall Progress -->
                    <div
                      class="mt-6 pt-4 border-t"
                      style="border-color: var(--border);"
                    >
                      <div class="flex items-center justify-between mb-2">
                        <span
                          class="text-xs font-medium"
                          style="color: var(--muted-foreground);"
                          >Overall Progress</span
                        >
                        <span
                          class="text-xs font-bold"
                          style="color: var(--foreground);"
                          >{currentProgress}%</span
                        >
                      </div>
                      <div
                        class="h-2 rounded-full overflow-hidden"
                        style="background: var(--muted);"
                      >
                        <div
                          class="h-full transition-all duration-500 ease-out"
                          style="background: linear-gradient(to right, var(--primary), var(--accent)); width: {currentProgress}%"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            {/if}
          </div>
        </div>

        <!-- Input Area -->
        <div
          class="input-wrapper input-wrapper-seo p-6 {isLoading
            ? 'opacity-50 pointer-events-none'
            : ''}"
        >
          <div class="flex" style="gap: 15px;">
            <div class="flex-1 relative">
              <textarea
                bind:value={input}
                on:keydown={handleKeyDown}
                placeholder={currentPlaceholder}
                class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full"
                style="min-height: 60px;"
                disabled={isLoading}
              ></textarea>
            </div>
            <button
              on:click={sendMessage}
              disabled={!input.trim() || isLoading}
              class="btn-primary px-6 font-bold flex items-center gap-2"
              style="height: auto; align-self: stretch;"
            >
              {#if isLoading}
                <div
                  class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"
                ></div>
              {:else}
                <Megaphone class="w-4 h-4 animate-pulse" />
              {/if}
              Orchestrate
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  @keyframes scale-in {
    0% {
      transform: scale(0);
      opacity: 0;
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }
</style>
